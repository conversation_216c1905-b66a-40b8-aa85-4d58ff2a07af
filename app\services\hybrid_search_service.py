"""
混合检索服务

负责：
- 混合检索（语义+全文）
- 不同检索策略
- 重排序处理
- 混合搜索结果优化
"""

import time
from typing import Dict, List, Any, Optional, Union
from app.config.settings import VectorDBConfig
from app.repositories import HybridRepository, VectorDatabaseRepository
from app.services.embedding_service import EmbeddingService
from app.utils.logging_utils import OperationLogger, TimeTracker
from app.utils.validation import ParameterValidator
from app.utils.response_utils import ResponseBuilder, SearchResponseBuilder
from app.utils.formatting import SearchResultFormatter


class HybridSearchService:
    """混合检索服务"""
    
    def __init__(self):
        """初始化混合检索服务"""
        self.db_repo = VectorDatabaseRepository()
        self.hybrid_repo: Optional[HybridRepository] = None
        self.embedding_service: Optional[EmbeddingService] = None
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self.search_response_builder = SearchResponseBuilder()
        self._initialized = False
    
    async def initialize(self, config: VectorDBConfig) -> Dict[str, Any]:
        """
        初始化混合检索服务
        
        Args:
            config: 数据库配置
            
        Returns:
            Dict[str, Any]: 初始化结果
        """
        start_time = time.time()
        
        try:
            # 设置数据库配置
            self.db_repo.set_config(config)
            
            # 获取数据库连接
            db_connection = await self.db_repo.get_database_connection()
            
            # 初始化hybrid repository
            self.hybrid_repo = HybridRepository(db_connection)
            
            # 初始化向量化服务
            self.embedding_service = EmbeddingService()
            
            self._initialized = True
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="混合检索服务初始化",
                initialization_time=f"{elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="混合检索服务初始化成功",
                data={
                    "initialization_time": elapsed_time,
                    "status": "ready"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="混合检索服务初始化",
                error=e
            )
            raise Exception(f"混合检索服务初始化失败: {str(e)}")
    
    def _ensure_initialized(self):
        """确保服务已初始化"""
        if not self._initialized:
            raise Exception("混合检索服务未初始化，请先调用initialize方法")
        if not self.hybrid_repo:
            raise Exception("混合检索仓库未初始化")
        if not self.embedding_service:
            raise Exception("向量化服务未初始化")
    

    
    async def hybrid_search(
        self,
        collection_name: str,
        query: str,
        top_k: int = 10,
        dense_weight: float = 0.7,
        sparse_weight: float = 0.3,
        search_strategy: str = "rrf",
        rerank_strategy: Optional[str] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        混合检索（语义+全文）
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            dense_weight: 密集向量权重
            sparse_weight: 稀疏向量权重
            search_strategy: 搜索策略 (rrf, weighted_avg)
            rerank_strategy: 重排序策略
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 混合检索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("混合检索")
        timer.start()
        
        try:
            # 参数验证
            if dense_weight + sparse_weight <= 0:
                raise ValueError("权重之和必须大于0")
            
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.hybrid_repo = HybridRepository(db_connection)
            
            # 生成查询向量
            embedding_result = await self.embedding_service.generate_embedding(query)
            query_vector = embedding_result['data']['vector']
            vector_gen_time = timer.checkpoint("查询向量生成")
            
            # 执行混合检索
            search_results = await self.hybrid_repo.search_hybrid_vectors(
                collection_name=collection_name,
                query_text=query,
                dense_vector=query_vector,
                top_k=top_k,
                dense_weight=dense_weight,
                sparse_weight=sparse_weight,
                search_strategy=search_strategy,
                rerank_strategy=rerank_strategy or ""
            )
            
            search_time = timer.checkpoint("混合检索执行")
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="混合检索",
                collection=collection_name,
                query_length=len(query),
                results_count=len(formatted_results),
                search_strategy=search_strategy,
                dense_weight=dense_weight,
                sparse_weight=sparse_weight,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.hybrid_search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                search_strategy=search_strategy,
                rerank_strategy=rerank_strategy,
                databases_searched=1
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="混合检索",
                error=e,
                collection=collection_name,
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"混合检索失败: {str(e)}")
    
    async def semantic_only_search(
        self,
        collection_name: str,
        query: str,
        top_k: int = 10,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        仅语义检索
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 语义检索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("语义检索")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.hybrid_repo = HybridRepository(db_connection)
            
            # 生成查询向量
            embedding_result = await self.embedding_service.generate_embedding(query)
            query_vector = embedding_result['data']['vector']
            vector_gen_time = timer.checkpoint("查询向量生成")
            
            # 执行语义检索
            search_results = await self.hybrid_repo.search_semantic_only(
                collection_name=collection_name,
                dense_vector=query_vector,
                top_k=top_k
            )
            
            search_time = timer.checkpoint("语义检索执行")
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="语义检索",
                collection=collection_name,
                query_length=len(query),
                results_count=len(formatted_results),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                collection=collection_name,
                database=database,
                time_details={
                    "vector_generation": f"{vector_gen_time:.3f}秒",
                    "search_execution": f"{search_time:.3f}秒"
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="语义检索",
                error=e,
                collection=collection_name,
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"语义检索失败: {str(e)}")
    
    async def full_text_only_search(
        self,
        collection_name: str,
        query: str,
        top_k: int = 10,
        output_fields: Optional[List[str]] = None,
        database: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        仅全文检索
        
        Args:
            collection_name: 集合名称
            query: 查询文本
            top_k: 返回结果数量
            output_fields: 输出字段
            database: 数据库名称
            
        Returns:
            Dict[str, Any]: 全文检索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("全文检索")
        timer.start()
        
        try:
            # 切换数据库连接
            if database:
                db_connection = await self.db_repo.get_database_connection(
                    database=database,
                    create_if_not_exists=True
                )
                self.hybrid_repo = HybridRepository(db_connection)
            
            # 执行全文检索
            search_results = await self.hybrid_repo.search_full_text_only(
                collection_name=collection_name,
                query_text=query,
                top_k=top_k
            )
            
            search_time = timer.checkpoint("全文检索执行")
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="全文检索",
                collection=collection_name,
                query_length=len(query),
                results_count=len(formatted_results),
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                collection=collection_name,
                database=database
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="全文检索",
                error=e,
                collection=collection_name,
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"全文检索失败: {str(e)}")
    
    async def multi_hybrid_search(
        self,
        collections: List[str],
        query: str,
        top_k: int = 10,
        dense_weight: float = 0.7,
        sparse_weight: float = 0.3,
        search_strategy: str = "hybrid",
        rerank_strategy: str = "rrf",
        rrf_k: int = 60,
        databases: Optional[List[str]] = None,
        output_fields: Optional[List[str]] = None,
        embedding_type: Optional[str] = None,
        filter_expr: Optional[str] = None,
        metadata_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        多集合混合检索
        
        Args:
            collections: 集合名称列表
            query: 查询文本
            top_k: 每个集合返回的结果数量
            dense_weight: 密集向量权重
            sparse_weight: 稀疏向量权重
            search_strategy: 搜索策略
            databases: 数据库名称列表
            output_fields: 输出字段
            embedding_type: 向量生成方法类型
            filter_expr: Milvus筛选表达式
            metadata_filters: metadata字段筛选条件

        Returns:
            Dict[str, Any]: 多集合混合检索结果
        """
        self._ensure_initialized()
        
        timer = TimeTracker("多集合混合检索")
        timer.start()
        
        try:
            # 参数验证
            if not collections:
                raise ValueError("集合列表不能为空")

            # 构建筛选表达式
            from app.utils.filter_utils import FilterExpressionBuilder
            final_filter_expr = ""

            # 处理筛选条件
            filter_conditions = []

            # 1. 直接的筛选表达式
            if filter_expr:
                filter_conditions.append(filter_expr)

            # 2. metadata筛选条件
            if metadata_filters:
                metadata_filter_expr = FilterExpressionBuilder.build_metadata_filter_expr(metadata_filters)
                if metadata_filter_expr:
                    filter_conditions.append(metadata_filter_expr)

            # 3. 组合筛选条件
            if filter_conditions:
                final_filter_expr = FilterExpressionBuilder.combine_filter_expressions(filter_conditions)

            # 生成查询向量（使用指定的embedding类型）
            embedding_result = await self.embedding_service.generate_embedding(
                query,
                embedding_type=embedding_type
            )
            query_vector = embedding_result['data']['vector']
            
            all_results = []
            search_errors = []
            
            # 逐个搜索集合
            for i, collection_name in enumerate(collections):
                try:
                    # 切换数据库
                    if databases and i < len(databases):
                        db_connection = await self.db_repo.get_database_connection(
                            database=databases[i],
                            create_if_not_exists=True
                        )
                        self.hybrid_repo = HybridRepository(db_connection)

                    # 处理None值，转换为'all'
                    collection_name = collection_name if collection_name is not None else 'all'

                    # 执行混合检索
                    collection_results = await self.hybrid_repo.search_hybrid_vectors(
                        collection_name=collection_name,
                        query_text=query,
                        dense_vector=query_vector,
                        top_k=top_k,
                        filter_expr=final_filter_expr,
                        dense_weight=dense_weight,
                        sparse_weight=sparse_weight,
                        search_strategy=search_strategy,
                        rerank_strategy=rerank_strategy,
                        rrf_k=rrf_k
                    )
                    
                    # 添加源信息到结果
                    for result in collection_results:
                        result['source_collection'] = collection_name
                        if databases and i < len(databases):
                            result['source_database'] = databases[i]
                    
                    all_results.extend(collection_results)
                    
                except Exception as e:
                    search_errors.append({
                        "collection": collection_name,
                        "database": databases[i] if databases and i < len(databases) else None,
                        "error": str(e)
                    })
            
            # 合并和排序结果
            if all_results:
                all_results = sorted(all_results, key=lambda x: x.get('similarity', 0), reverse=True)
                # 限制总结果数量
                all_results = all_results[:top_k * len(collections)]
            
            # 格式化结果
            formatted_results = SearchResultFormatter.format_search_results(all_results)
            
            elapsed_time = timer.finish()
            
            self.operation_logger.log_operation_success(
                operation_name="多集合混合检索",
                collections_count=len(collections),
                results_count=len(formatted_results),
                errors_count=len(search_errors),
                search_strategy=search_strategy,
                total_time=f"{elapsed_time['total']:.3f}秒"
            )
            
            return self.search_response_builder.hybrid_search_response(
                query=query,
                results=formatted_results,
                search_time=f"{elapsed_time['total']:.3f}秒",
                search_strategy=search_strategy,
                databases_searched=len(set(databases) if databases else [None]),
                errors=search_errors if search_errors else None
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="多集合混合检索",
                error=e,
                collections_count=len(collections),
                query=query[:50] + "..." if len(query) > 50 else query
            )
            raise Exception(f"多集合混合检索失败: {str(e)}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """
        获取服务状态
        
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        return {
            "initialized": self._initialized,
            "components": {
                "hybrid_repository": "active" if self.hybrid_repo else "inactive",
                "embedding_service": "active" if self.embedding_service else "inactive",
                "database_repository": "active"
            }
        }