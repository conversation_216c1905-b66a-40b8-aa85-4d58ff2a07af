"""
向量生成服务

负责：
- 文本向量化处理
- 多种向量模型支持
- 批量向量生成
- 向量缓存管理
"""

import time
import asyncio
from typing import List, Optional, Dict, Any, Union
from app.core.embedding import EmbeddingModel
from app.dependencies import get_embed_model_sync
from app.utils.logging_utils import OperationLogger
from app.utils.validation import ParameterValidator
from app.utils.response_utils import ResponseBuilder


class EmbeddingService:
    """向量生成服务"""
    
    def __init__(self):
        """初始化向量生成服务"""
        self.operation_logger = OperationLogger()
        self.validator = ParameterValidator()
        self.response_builder = ResponseBuilder()
        self._model_cache: Dict[str, EmbeddingModel] = {}
    
    async def get_embedding_model(self, embedding_type: Optional[str] = None) -> EmbeddingModel:
        """
        获取向量模型实例
        
        Args:
            embedding_type: 向量模型类型
            
        Returns:
            EmbeddingModel: 向量模型实例
        """
        try:
            # 使用缓存避免重复创建模型
            cache_key = embedding_type or "default"
            
            if cache_key not in self._model_cache:
                if embedding_type:
                    model = await get_embed_model_sync(embedding_type)
                else:
                    model = await get_embed_model_sync()
                
                self._model_cache[cache_key] = model
            
            return self._model_cache[cache_key]
            
        except Exception as e:
            raise Exception(f"获取向量模型失败: {str(e)}")
    
    async def generate_embedding(
        self,
        text: str,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成单个文本的向量
        
        Args:
            text: 输入文本
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 向量生成结果
        """
        start_time = time.time()
        
        try:
            # 参数验证
            if not text or not text.strip():
                raise ValueError("输入文本不能为空")
            
            # 获取向量模型
            model = await self.get_embedding_model(embedding_type)
            
            # 生成向量
            vector = await model.agenerate(text.strip())
            
            elapsed_time = time.time() - start_time
            
            self.operation_logger.log_operation_success(
                operation_name="向量生成",
                details=f"文本长度: {len(text)}, 向量维度: {len(vector)}, 耗时: {elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message="向量生成成功",
                data={
                    "vector": vector,
                    "dimension": len(vector),
                    "model_type": embedding_type or "default",
                    "text_length": len(text),
                    "generation_time": elapsed_time
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="向量生成",
                error=e
            )
            raise Exception(f"向量生成失败: {str(e)}")
    
    async def generate_embeddings_batch(
        self,
        texts: List[str],
        embedding_type: Optional[str] = None,
        batch_size: int = 10
    ) -> Dict[str, Any]:
        """
        批量生成文本向量
        
        Args:
            texts: 文本列表
            embedding_type: 向量模型类型
            batch_size: 批处理大小
            
        Returns:
            Dict[str, Any]: 批量向量生成结果
        """
        start_time = time.time()
        
        try:
            # 参数验证
            if not texts:
                raise ValueError("文本列表不能为空")
            
            # 过滤空文本
            valid_texts = [text.strip() for text in texts if text and text.strip()]
            if not valid_texts:
                raise ValueError("没有有效的文本内容")
            
            # 获取向量模型
            model = await self.get_embedding_model(embedding_type)
            
            # 检查模型类型并使用相应的批量处理方法
            if hasattr(model, 'model') and hasattr(getattr(model, 'model', None), 'encode'):
                # HuggingFace SentenceTransformer 模型 - 使用原生批量处理
                print(f"使用HuggingFace批量向量生成: {len(valid_texts)} 个文本")
                
                # 确保模型已加载
                if not hasattr(model, 'model'):
                    load_model_method = getattr(model, 'load_model', None)
                    if load_model_method:
                        load_model_method()
                
                # 使用原生批量编码 - 异步优化
                sentence_model = getattr(model, 'model', None)
                if sentence_model:
                    # 使用asyncio.to_thread将同步操作转为异步，避免阻塞事件循环
                    embeddings = await asyncio.to_thread(
                        sentence_model.encode,
                        valid_texts,
                        show_progress_bar=True,
                        batch_size=32
                    )
                    
                    # 转换为列表格式
                    if hasattr(embeddings, 'tolist'):
                        vectors = embeddings.tolist()
                    else:
                        vectors = []
                        for embedding in embeddings:
                            if hasattr(embedding, 'tolist'):
                                vectors.append(embedding.tolist())
                            else:
                                vectors.append(list(embedding))
                    
                    print(f"HuggingFace批量向量生成完成: {len(vectors)} 个向量")
                else:
                    raise Exception("无法访问HuggingFace模型实例")
            
            elif hasattr(model, '_get_client') and hasattr(model, 'model_name'):
                # OpenAI兼容模型 (Qwen3, Azure OpenAI, BgeM3) - 使用批量API
                model_type = model.__class__.__name__
                print(f"使用{model_type}批量向量生成: {len(valid_texts)} 个文本")
                
                client_method = getattr(model, '_get_client', None)
                if not client_method:
                    raise Exception(f"无法获取{model_type}客户端")
                
                client = client_method()
                model_name = getattr(model, 'model_name', 'unknown')
                
                # 根据模型类型调用不同的API参数 - 异步优化
                if model.__class__.__name__ == 'Qwen3Embedding':
                    # Qwen3模型支持批量输入和dimensions参数
                    dimension = getattr(model, 'dimension', 1024)
                    response = await asyncio.to_thread(
                        client.embeddings.create,
                        input=valid_texts,
                        model=model_name,
                        dimensions=dimension,
                        encoding_format="float"
                    )
                elif model.__class__.__name__ == 'AzureOpenAIEmbedding':
                    # Azure OpenAI模型支持批量输入
                    response = await asyncio.to_thread(
                        client.embeddings.create,
                        input=valid_texts,
                        model=model_name
                    )
                elif model.__class__.__name__ == 'BgeM3Embedding':
                    # BgeM3模型支持批量输入
                    response = await asyncio.to_thread(
                        client.embeddings.create,
                        input=valid_texts,
                        model=model_name
                    )
                else:
                    # 通用OpenAI兼容模型
                    response = await asyncio.to_thread(
                        client.embeddings.create,
                        input=valid_texts,
                        model=model_name
                    )
                
                # 提取所有向量
                vectors = [data.embedding for data in response.data]
                print(f"{model_type}批量向量生成完成: {len(vectors)} 个向量")
            
            else:
                # 其他模型 - 使用逐个处理但减少进度输出
                model_type = model.__class__.__name__
                print(f"使用{model_type}逐个向量生成: {len(valid_texts)} 个文本")
                vectors = []
                
                for i, text in enumerate(valid_texts):
                    vector = await model.agenerate(text)
                    vectors.append(vector)
                    
                    # 只在特定间隔显示进度，避免日志过多
                    if (i + 1) % max(1, len(valid_texts) // 5) == 0 or i == len(valid_texts) - 1:
                        progress = ((i + 1) / len(valid_texts)) * 100
                        print(f"向量生成进度: {i + 1}/{len(valid_texts)} ({progress:.1f}%)")
            
            elapsed_time = time.time() - start_time
            avg_time_per_text = elapsed_time / len(vectors) if vectors else 0
            
            self.operation_logger.log_operation_success(
                operation_name="批量向量生成",
                details=f"文本数量: {len(vectors)}, 向量维度: {len(vectors[0]) if vectors else 0}, 总耗时: {elapsed_time:.3f}秒"
            )
            
            return self.response_builder.success_response(
                message=f"批量向量生成成功，共处理 {len(vectors)} 个文本",
                data={
                    "vectors": vectors,
                    "count": len(vectors),
                    "dimension": len(vectors[0]) if vectors else 0,
                    "model_type": embedding_type or "default",
                    "total_time": elapsed_time,
                    "avg_time_per_text": avg_time_per_text,
                    "batch_size": batch_size
                }
            )
            
        except Exception as e:
            self.operation_logger.log_operation_error(
                operation_name="批量向量生成",
                error=e
            )
            raise Exception(f"批量向量生成失败: {str(e)}")
    
    async def get_embedding_info(
        self,
        embedding_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取向量模型信息
        
        Args:
            embedding_type: 向量模型类型
            
        Returns:
            Dict[str, Any]: 模型信息
        """
        try:
            model = await self.get_embedding_model(embedding_type)
            
            return self.response_builder.success_response(
                message="获取模型信息成功",
                data={
                    "model_type": embedding_type or "default",
                    "dimension": model.get_dimension(),
                    "model_name": getattr(model, 'model_name', 'unknown'),
                    "device": getattr(model, 'device', 'unknown'),
                    "cache_status": "cached" if (embedding_type or "default") in self._model_cache else "not_cached"
                }
            )
            
        except Exception as e:
            raise Exception(f"获取模型信息失败: {str(e)}")
    
    def clear_model_cache(self):
        """清空模型缓存"""
        self._model_cache.clear()
        
    def get_cache_status(self) -> Dict[str, Any]:
        """获取缓存状态"""
        return {
            "cached_models": list(self._model_cache.keys()),
            "cache_size": len(self._model_cache)
        } 