"""
FastAPI路由层 - 重构版本

使用服务层架构，routes层只负责：
1. API路由定义
2. 请求参数验证
3. 调用相应的服务
4. 响应格式化

业务逻辑已迁移到services层
"""

from fastapi import APIRouter, Depends, File, UploadFile, Form, HTTPException, Request
from app.core.embedding import EmbeddingModel
from typing import Optional, Dict, Any
from app.dependencies import get_embed_model_sync, get_vectordb_config_sync

# 导入服务层
from app.services.document_service import DocumentService
from app.services.search_service import SearchService
from app.services.vector_service import VectorService
from app.services.hybrid_search_service import HybridSearchService
from app.services.embedding_service import EmbeddingService

# 导入仓库层
from app.repositories.search_repository import SearchRepository

# 导入utils层工具函数
from app.utils.filter_utils import FilterExpressionBuilder
from app.utils.logging_utils import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    OperationLogger, 
    ParameterLogger, 
    <PERSON>sul<PERSON><PERSON><PERSON><PERSON>,
    <PERSON>bu<PERSON><PERSON><PERSON><PERSON>,
    Log<PERSON>onfig,
    configure_logging
)
from app.utils.validation import validate_upload_parameters, validate_search_parameters, ValidationError

# 导入schemas层数据模型
from app.schemas import (
    TextUploadRequest,
    BatchTextUploadRequest,
    HybridTextUploadRequest,
    SearchRequest,
    MultiDBSearchRequest,
    HybridMultiDBSearchRequest,
    UpsertRequest,
    DeleteRequest,
    EmbeddingRequest
)

# 全局服务实例
document_service = DocumentService()
search_service = SearchService()
vector_service = VectorService()
hybrid_search_service = HybridSearchService()
embedding_service_global = EmbeddingService()

# 服务初始化标志
services_initialized = False

# 配置日志系统
log_config = LogConfig(
    log_root_dir="logs",
    max_file_size_mb=100,
    retention_days=30,
    async_write=True,
    buffer_size=100,
    flush_interval=1.0
)
configure_logging(log_config)

async def initialize_services():
    """初始化所有服务"""
    global services_initialized
    if not services_initialized:
        try:
            config = get_vectordb_config_sync()
            await document_service.initialize(config)
            await search_service.initialize(config)
            await vector_service.initialize(config)
            await hybrid_search_service.initialize(config)
            services_initialized = True
            OperationLogger.log_operation_success("服务初始化", "management")
        except Exception as e:
            OperationLogger.log_operation_error("服务初始化", e, "management")
            raise

# 路由定义
router = APIRouter(prefix="/api/v1")


# 路由处理函数
@router.post("/upload",
    tags=["Document Processing"],
    summary="上传并处理文档"
)
async def upload_document(
    file: UploadFile = File(..., description="上传的文档文件"),
    collection: str = Form("documents", description="文档所属的集合名称，用于区分不同业务场景"),
    database: Optional[str] = Form(None, description="要使用的数据库名称，不指定则使用默认数据库"),
    chunk_size: str = Form("500", description="分块大小"),
    chunk_overlap: str = Form("50", description="分块重叠大小"),
    encrypt: bool = Form(False, description="是否对上传内容进行AES加密")
) -> Dict[str, Any]:
    """文档上传和处理 - 通过DocumentService处理"""
    await initialize_services()
    
    try:
        # 使用统一的参数验证工具
        chunk_size_int, chunk_overlap_int = validate_upload_parameters(chunk_size, chunk_overlap)
        
        OperationLogger.log_operation_start("文档上传", "upload", 
                                           filename=file.filename, 
                                           collection=collection,
                                           chunk_size=chunk_size_int,
                                           chunk_overlap=chunk_overlap_int)
        
        # 调用DocumentService处理文档上传
        result = await document_service.upload_document(
            file=file,
            collection_name=collection,
            chunk_size=chunk_size_int,
            chunk_overlap=chunk_overlap_int,
            encrypt=encrypt,
            database=database
        )
        
        OperationLogger.log_operation_success("文档上传", "upload", 
                                            filename=file.filename,
                                            result_count=result.get('count', 0))
        return result
        
    except ValidationError as e:
        ParameterLogger.log_validation_result("upload_params", 
                                            {"chunk_size": chunk_size, "chunk_overlap": chunk_overlap}, 
                                            False, str(e), "upload")
        raise HTTPException(400, f"参数验证失败: {str(e)}")
    except Exception as e:
        OperationLogger.log_operation_error("文档上传", e, "upload", 
                                          filename=file.filename,
                                          collection=collection)
        raise HTTPException(500, f"文档上传失败: {str(e)}")

@router.post("/embeddings")
async def create_embedding(
    request: EmbeddingRequest,
    model: EmbeddingModel = Depends(get_embed_model_sync, use_cache=True)
) -> Dict[str, Any]:
    """生成向量 - 通过EmbeddingService处理"""
    try:
        OperationLogger.log_operation_start("向量生成", "management", 
                                           text_preview=request.text[:50])
        
        # 直接使用模型生成向量（这是简单操作，不需要走服务层）
        vector = await model.agenerate(request.text)
        
        ResultLogger.log_vector_generation(request.text, len(vector), 0, "management")
        OperationLogger.log_operation_success("向量生成", "management", 
                                            vector_dim=len(vector),
                                            text_length=len(request.text))
        return {"text": request.text, "vector": vector}
        
    except Exception as e:
        OperationLogger.log_operation_error("向量生成", e, "management", 
                                          text_preview=request.text[:50])
        raise HTTPException(500, f"向量生成失败: {str(e)}")

@router.post("/upload_texts",
    tags=["Document Processing"],
    summary="上传文本列表并处理为向量"
)
async def upload_texts(
    request: TextUploadRequest,
) -> Dict[str, Any]:
    """文本列表上传 - 通过DocumentService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request)
        
        # 验证参数
        if not request.texts:
            raise HTTPException(400, "文本列表不能为空")
        
        if request.ids is not None and len(request.ids) != len(request.texts):
            raise HTTPException(400, f"ids列表长度({len(request.ids)})必须与texts列表长度({len(request.texts)})一致")
        
        OperationLogger.log_operation_start("文本列表上传", "upload", 
                                           text_count=len(request.texts),
                                           collection=request.collection)
        
        # 转换请求格式以匹配DocumentService接口
        metadata_list = [request.metadata] * len(request.texts) if request.metadata else None

        # 提取额外字段（类似upload_texts_batch的处理方式）
        extra_fields = {}
        if hasattr(request, 'model_dump'):
            all_fields = request.model_dump()
        else:
            all_fields = request.dict()

        # 标准字段列表
        standard_fields = ["texts", "collection", "database", "metadata", "encrypt", "embedding_type", "ids"]

        # 提取非标准字段作为额外字段
        for key, value in all_fields.items():
            if key not in standard_fields and value is not None:
                extra_fields[key] = value

        # 调用DocumentService处理文本上传
        result = await document_service.upload_texts(
            texts=request.texts,
            collection_name=request.collection,
            metadata=metadata_list,
            ids=request.ids,
            encrypt=request.encrypt,
            database=request.database,
            embedding_type=request.embedding_type,
            extra_fields=extra_fields  # 传递额外字段
        )
        
        OperationLogger.log_operation_success("文本列表上传", "upload", 
                                            text_count=len(request.texts),
                                            result_count=result.get('count', 0))
        return result
        
    except Exception as e:
        OperationLogger.log_operation_error("文本列表上传", e, "upload", 
                                          text_count=len(request.texts) if hasattr(request, 'texts') else 0,
                                          collection=request.collection)
        raise HTTPException(500, f"文本列表上传失败: {str(e)}")

@router.post("/upload_texts_batch",
    tags=["Document Processing"],
    summary="批量上传文本列表并处理为向量（支持并发处理和每个文本独立metadata）"
)
async def upload_texts_batch(
    request: BatchTextUploadRequest
) -> Dict[str, Any]:
    """批量文本上传 - 通过DocumentService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request)
        
        if not request.items:
            raise HTTPException(400, "文本项列表不能为空")
        
        # 验证是否有重复的自定义ID
        custom_ids = [item.id for item in request.items if item.id is not None]
        if custom_ids and len(custom_ids) != len(set(custom_ids)):
            raise HTTPException(400, "存在重复的自定义ID")
        
        OperationLogger.log_operation_start("批量文本上传", "upload", 
                                           item_count=len(request.items),
                                           collection=request.collection,
                                           batch_size=request.batch_size)
        
        # 转换请求格式以匹配DocumentService接口
        items_dict = []
        for item in request.items:
            item_dict = {
                "text": item.text,
                "metadata": item.metadata,
                "id": item.id
            }
            # 添加额外字段
            if hasattr(item, 'model_dump'):
                extra_fields = item.model_dump()
            else:
                extra_fields = item.dict()
            
            for key, value in extra_fields.items():
                if key not in ["text", "metadata", "id"] and value is not None:
                    item_dict[key] = value
            
            items_dict.append(item_dict)
        
        # 调用DocumentService处理批量上传
        result = await document_service.upload_texts_batch(
            items=items_dict,
            collection_name=request.collection,
            global_metadata=request.global_metadata,
            encrypt=request.encrypt,
            batch_size=request.batch_size,
            database=request.database,
            embedding_type=request.embedding_type
        )
        
        OperationLogger.log_operation_success("批量文本上传", "upload", 
                                            item_count=len(request.items),
                                            result_count=result.get('count', 0),
                                            batch_size=request.batch_size)
        return result
        
    except Exception as e:
        OperationLogger.log_operation_error("批量文本上传", e, "upload", 
                                          item_count=len(request.items) if hasattr(request, 'items') else 0,
                                          collection=request.collection)
        raise HTTPException(500, f"批量文本上传失败: {str(e)}")

@router.post("/upload_texts_batch_hybrid",
    tags=["Document Processing"],
    summary="批量上传文本列表并处理为混合检索向量（支持全文检索+语义检索）"
)
async def upload_texts_batch_hybrid(
    request: HybridTextUploadRequest
) -> Dict[str, Any]:
    """混合检索批量上传 - 通过DocumentService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request)
        
        if not request.items:
            raise HTTPException(400, "文本项列表不能为空")
        
        OperationLogger.log_operation_start("混合检索批量上传", "upload", 
                                           item_count=len(request.items),
                                           collection=request.collection,
                                           batch_size=request.batch_size)
        
        # 转换请求格式
        items_dict = []
        for item in request.items:
            item_dict = {
                "text": item.text,
                "metadata": item.metadata,
                "id": item.id
            }
            # 添加额外字段
            if hasattr(item, 'model_dump'):
                extra_fields = item.model_dump()
            else:
                extra_fields = item.dict()
            
            for key, value in extra_fields.items():
                if key not in ["text", "metadata", "id"] and value is not None:
                    item_dict[key] = value
            
            items_dict.append(item_dict)
        
        # 调用DocumentService处理混合检索上传
        result = await document_service.upload_texts_batch_hybrid(
            items=items_dict,
            collection_name=request.collection,
            global_metadata=request.global_metadata,
            encrypt=request.encrypt,
            batch_size=request.batch_size,
            database=request.database,
            embedding_type=request.embedding_type
        )
        
        OperationLogger.log_operation_success("混合检索批量上传", "upload", 
                                            item_count=len(request.items),
                                            result_count=result.get('count', 0),
                                            batch_size=request.batch_size)
        return result
        
    except Exception as e:
        OperationLogger.log_operation_error("混合检索批量上传", e, "upload", 
                                          item_count=len(request.items) if hasattr(request, 'items') else 0,
                                          collection=request.collection)
        raise HTTPException(500, f"混合检索批量上传失败: {str(e)}")

@router.post("/search")
async def vector_search(
    request: SearchRequest,
    raw_request: Request
) -> Dict[str, Any]:
    """向量搜索 - 通过SearchService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request, raw_request)
        
        # 使用统一的参数验证工具
        top_k, _ = validate_search_parameters(request.top_k)
        
        OperationLogger.log_operation_start("向量搜索", "search", 
                                           query_preview=request.text[:50],
                                           collection=request.collection,
                                           top_k=top_k)
        
        # 处理筛选表达式
        filter_expr = ""
        filter_conditions = []
        
        if request.filter_expr:
            filter_conditions.append(request.filter_expr)
        
        if request.metadata_filters:
            metadata_filter_expr = FilterExpressionBuilder.build_metadata_filter_expr(request.metadata_filters)
            if metadata_filter_expr:
                filter_conditions.append(metadata_filter_expr)
        
        # 检查额外字段用于筛选
        if not filter_conditions:
            standard_fields = ["text", "top_k", "collection", "database", "embedding_type", "filter_expr", "metadata_filters"]
            try:
                all_fields = request.model_dump(exclude_unset=True) if hasattr(request, 'model_dump') else request.dict(exclude_unset=True)
            except:
                all_fields = {}
            
            for field, value in all_fields.items():
                if field not in standard_fields and value is not None:
                    if isinstance(value, str):
                        filter_conditions.append(f"{field} == '{value}'")
                    elif isinstance(value, (int, float, bool)):
                        filter_conditions.append(f"{field} == {value}")
        
        if filter_conditions:
            filter_expr = " and ".join(filter_conditions)
        
        # 如果指定了embedding_type，需要特殊处理
        if request.embedding_type:
            # 使用指定的embedding模型生成向量
            from app.services.embedding_service import EmbeddingService
            embedding_service = EmbeddingService()
            embedding_result = await embedding_service.generate_embedding(
                request.text, 
                embedding_type=request.embedding_type
            )
            query_vector = embedding_result['data']['vector']
            
            # 确保search_service已初始化
            search_service._ensure_initialized()
            
            if request.database:
                # 切换到指定的数据库
                db_connection = await search_service.db_repo.get_database_connection(request.database)
                search_service.search_repo = SearchRepository(db_connection)
            
            # 直接使用search_repo进行搜索
            search_results = await search_service.search_repo.search_vectors(
                collection_name=request.collection,
                query_vector=query_vector,
                top_k=top_k,
                filter_expr=filter_expr if filter_expr else ""
            )
            
            # 格式化结果
            from app.utils.formatting import SearchResultFormatter
            formatted_results = SearchResultFormatter.format_search_results(search_results)
            
            result = search_service.search_response_builder.search_response(
                query=request.text,
                results=formatted_results,
                search_time="N/A",
                collection=request.collection,
                database=request.database,
                time_details={"embedding_type": request.embedding_type}
            )
        else:
            # 使用默认的搜索流程
            result = await search_service.search_vectors(
                collection_name=request.collection,
                query=request.text,
                top_k=top_k,
                filter_expr=filter_expr if filter_expr else None,
                database=request.database
            )
        
        # 记录搜索结果
        ResultLogger.log_search_results_summary(result.get('results', []), category="search")
        OperationLogger.log_operation_success("向量搜索", "search", 
                                            query_preview=request.text[:50],
                                            result_count=len(result.get('results', [])),
                                            collection=request.collection)
        return result
        
    except ValueError as e:
        ParameterLogger.log_validation_result("search_params", 
                                            {"top_k": request.top_k, "text": request.text[:50]}, 
                                            False, str(e), "search")
        raise HTTPException(400, f"参数错误: {str(e)}")
    except Exception as e:
        OperationLogger.log_operation_error("向量搜索", e, "search", 
                                          query_preview=request.text[:50],
                                          collection=request.collection)
        raise HTTPException(500, f"向量搜索失败: {str(e)}")

@router.post("/multi_search",
    tags=["Vector Search"],
    summary="同时搜索多个向量库"
)
async def multi_vector_search(
    request: MultiDBSearchRequest,
    raw_request: Request
) -> Dict[str, Any]:
    """多数据库搜索 - 通过SearchService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request, raw_request)
        
        # 使用统一的参数验证工具
        top_k, total_results = validate_search_parameters(request.top_k, request.total_results)
        
        OperationLogger.log_operation_start("多数据库搜索", "search", 
                                           query_preview=request.text[:50],
                                           database_count=len(request.databases),
                                           top_k=top_k)
        
        # 解析数据库配置
        collections = []
        databases = []
        
        for db_item in request.databases:
            if isinstance(db_item, str):
                databases.append(db_item)
                collections.append('all')  # 'all'表示搜索所有集合
            else:
                # 如果指定了集合列表，需要为每个集合创建单独的数据库-集合对
                if db_item.collections:
                    for collection in db_item.collections:
                        databases.append(db_item.database)
                        collections.append(collection)
                else:
                    # 如果没有指定集合，搜索该数据库的所有集合
                    databases.append(db_item.database)
                    collections.append('all')
        
        # 调用SearchService进行多集合搜索
        result = await search_service.search_multiple_collections(
            collections=collections or ['all'],
            query=request.text,
            top_k=top_k,
            databases=databases,
            embedding_type=request.embedding_type  # 传递embedding_type给Service层处理
        )
        
        # 限制总结果数量
        if result.get('results') and total_results and len(result['results']) > total_results:
            result['results'] = result['results'][:total_results]
            result['total_results'] = len(result['results'])
        
        # 添加搜索的数据库数量信息
        result['databases_searched'] = len(databases)
        
        # 记录搜索结果
        ResultLogger.log_search_results_summary(result.get('results', []), category="search")
        OperationLogger.log_operation_success("多数据库搜索", "search", 
                                            query_preview=request.text[:50],
                                            database_count=len(databases),
                                            result_count=len(result.get('results', [])))
        return result
        
    except ValueError as e:
        ParameterLogger.log_validation_result("multi_search_params", 
                                            {"top_k": request.top_k, "total_results": request.total_results}, 
                                            False, str(e), "search")
        raise HTTPException(400, f"参数错误: {str(e)}")
    except Exception as e:
        OperationLogger.log_operation_error("多数据库搜索", e, "search", 
                                          query_preview=request.text[:50],
                                          database_count=len(request.databases))
        raise HTTPException(500, f"多数据库搜索失败: {str(e)}")

@router.post("/upsert",
    tags=["Vector Operations"],
    summary="更新或插入向量记录（支持自动向量生成）"
)
async def upsert_vectors(
    request: UpsertRequest,
    model: EmbeddingModel = Depends(get_embed_model_sync)
) -> Dict[str, Any]:
    """向量Upsert操作 - 通过VectorService处理"""
    await initialize_services()
    
    try:
        if not request.records:
            raise HTTPException(400, "记录列表不能为空")
        
        OperationLogger.log_operation_start("Upsert操作", "management", 
                                           record_count=len(request.records),
                                           collection=request.collection)
        
        # 分析记录，提取需要生成向量的文本
        texts_for_vector = []
        metadata_list = []
        ids_list = []
        
        for record in request.records:
            if 'vector' not in record:
                # 需要生成向量
                text_content = record.get('content') or record.get('text')
                if not text_content:
                    raise HTTPException(400, "记录既没有'vector'字段，也没有'content'或'text'字段用于生成向量")
                texts_for_vector.append(text_content)
                metadata_list.append(record.get('metadata', {}))
                ids_list.append(record.get('id'))
            else:
                # 已有向量，直接添加到文本列表（VectorService会处理）
                texts_for_vector.append(record.get('content', record.get('text', '')))
                metadata_list.append(record.get('metadata', {}))
                ids_list.append(record.get('id'))
        
        # 调用VectorService进行Upsert操作
        result = await vector_service.upsert_vectors(
            collection_name=request.collection,
            texts=texts_for_vector,
            metadata=metadata_list,
            ids=ids_list,
            auto_flush=request.auto_flush,
            database=request.database
        )
        
        OperationLogger.log_operation_success("Upsert操作", "management", 
                                            record_count=len(request.records),
                                            collection=request.collection,
                                            result_count=result.get('count', 0))
        return result
        
    except Exception as e:
        OperationLogger.log_operation_error("Upsert操作", e, "management", 
                                          record_count=len(request.records) if hasattr(request, 'records') else 0,
                                          collection=request.collection)
        raise HTTPException(500, f"Upsert操作失败: {str(e)}")

@router.post("/delete",
    tags=["Vector Operations"],
    summary="删除向量记录"
)
async def delete_vectors(
    request: DeleteRequest
) -> Dict[str, Any]:
    """向量删除操作 - 通过VectorService处理"""
    await initialize_services()
    
    try:
        # 验证参数
        if not request.ids and not request.filter_expr:
            raise HTTPException(400, "必须指定 'ids' 或 'filter_expr' 其中一个参数")
        
        if request.ids and request.filter_expr:
            raise HTTPException(400, "不能同时指定 'ids' 和 'filter_expr' 参数")
        
        OperationLogger.log_operation_start("删除操作", "management", 
                                           collection=request.collection,
                                           filter_expr=request.filter_expr,
                                           ids_count=len(request.ids) if request.ids else 0)
        
        # 调用VectorService进行删除操作
        result = await vector_service.delete_vectors(
            collection_name=request.collection,
            filter_expr=request.filter_expr,
            ids=request.ids,
            auto_flush=request.auto_flush,
            database=request.database
        )
        
        OperationLogger.log_operation_success("删除操作", "management", 
                                            collection=request.collection,
                                            deleted_count=result.get('deleted_count', 0))
        return result
        
    except Exception as e:
        OperationLogger.log_operation_error("删除操作", e, "management", 
                                          collection=request.collection,
                                          filter_expr=request.filter_expr)
        raise HTTPException(500, f"删除操作失败: {str(e)}")

@router.post("/multi_search_hybrid",
    tags=["Vector Search"],
    summary="同时搜索多个向量库（混合检索：语义检索+全文检索）"
)
async def multi_vector_search_hybrid(
    request: HybridMultiDBSearchRequest,
    raw_request: Request
) -> Dict[str, Any]:
    """多数据库混合检索 - 通过HybridSearchService处理"""
    await initialize_services()
    
    try:
        RequestLogger.log_request_info(request, raw_request)
        
        # 使用统一的参数验证工具
        top_k, total_results = validate_search_parameters(request.top_k, request.total_results)
        
        OperationLogger.log_operation_start("混合检索多数据库搜索", "search", 
                                           query_preview=request.text[:50],
                                           database_count=len(request.databases),
                                           search_strategy=request.search_strategy,
                                           rerank_strategy=request.rerank_strategy)
        
        # 解析数据库配置
        collections = []
        databases = []
        
        for db_item in request.databases:
            if isinstance(db_item, str):
                databases.append(db_item)
                collections.append('all')  # 'all'表示搜索所有集合
            else:
                # 为每个集合添加对应的数据库名称
                if db_item.collections:
                    for collection in db_item.collections:
                        databases.append(db_item.database)
                        collections.append(collection)
                else:
                    databases.append(db_item.database)
                    collections.append('all')  # 'all'表示搜索所有集合
        
        # 调用HybridSearchService进行多集合混合搜索
        result = await hybrid_search_service.multi_hybrid_search(
            collections=collections or ['all'],
            query=request.text,
            top_k=top_k,
            dense_weight=request.dense_weight,
            sparse_weight=request.sparse_weight,
            search_strategy=request.search_strategy,
            rerank_strategy=request.rerank_strategy,
            rrf_k=request.rrf_k,
            databases=databases,
            embedding_type=request.embedding_type,  # 传递embedding_type给Service层处理
            filter_expr=request.filter_expr,  # 传递筛选表达式
            metadata_filters=request.metadata_filters  # 传递metadata筛选条件
        )
        
        # 添加混合检索特有信息
        result.update({
            'databases_searched': len(databases),
            'search_strategy': request.search_strategy,
            'rerank_strategy': request.rerank_strategy
        })

        # 限制总结果数量（必须在result.update()之后，避免被覆盖）
        data_results = result.get('data', {}).get('results', [])
        if data_results and total_results and len(data_results) > total_results:
            result['data']['results'] = data_results[:total_results]
            result['data']['total_results'] = len(result['data']['results'])
        else:
            print(f"[调试] 未执行限制")
        
        # 记录搜索结果
        ResultLogger.log_search_results_summary(result.get('results', []), category="search")
        OperationLogger.log_operation_success("混合检索多数据库搜索", "search", 
                                            query_preview=request.text[:50],
                                            database_count=len(databases),
                                            result_count=len(result.get('results', [])),
                                            search_strategy=request.search_strategy)
        return result
        
    except ValueError as e:
        ParameterLogger.log_validation_result("hybrid_search_params", 
                                            {"top_k": request.top_k, "dense_weight": request.dense_weight}, 
                                            False, str(e), "search")
        raise HTTPException(400, f"参数错误: {str(e)}")
    except Exception as e:
        OperationLogger.log_operation_error("混合检索多数据库搜索", e, "search", 
                                          query_preview=request.text[:50],
                                          database_count=len(request.databases))
        
        # 构建错误响应，保持与原API的兼容性
        return {
            "query": request.text,
            "results": [],
            "total_results": 0,
            "search_time": "0.000秒",
            "databases_searched": len(request.databases),
            "search_strategy": request.search_strategy,
            "rerank_strategy": request.rerank_strategy,
            "errors": [{"error": str(e), "database": "multiple"}],
            "error_count": 1
        }