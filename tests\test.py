# from openai import AzureOpenAI

# client = AzureOpenAI(
#                 api_key='********************************',
#                 api_version="2024-10-21",
#                 azure_deployment='SVW-EMBEDDING-LARGE',
#                 azure_endpoint="https://openai-svw2.openai.azure.com/"
#             )

# response = client.embeddings.create(
#                 input='你好',
#                 model="text-embedding-3-large"
#             )
# print(response)



from openai import AsyncAzureOpenAI
client = AsyncAzureOpenAI(
                api_key='********************************',
                api_version="2024-10-21",
                azure_deployment='SVW-EMBEDDING-LARGE',
                azure_endpoint="https://openai-svw2.openai.azure.com/"
            )

async def main():
    response = await client.embeddings.create(
                input=['你好', '你是谁'],
                model="text-embedding-3-large"
            )
    return response

import asyncio
results = asyncio.run(main())
print(results)